#!/usr/bin/env python3
"""
调用本地 playwright-mcp 服务器打开谷歌
方法1: 使用 HTTP 请求调用 MCP 服务器
方法2: 直接使用 requests 库发送 JSON-RPC 请求
"""

import requests
import json
import time

def call_playwright_mcp_http():
    """通过 HTTP 调用 playwright-mcp 服务器"""

    # MCP 服务器的 URL (假设运行在 localhost:8931)
    mcp_url = "http://localhost:8931"

    try:
        # 1. 首先检查服务器是否运行
        print("检查 MCP 服务器状态...")

        # 2. 发送导航请求到谷歌
        navigate_payload = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "tools/call",
            "params": {
                "name": "playwright_navigate",
                "arguments": {
                    "url": "https://www.google.com"
                }
            }
        }

        headers = {
            "Content-Type": "application/json"
        }

        print("发送导航请求到谷歌...")
        response = requests.post(
            f"{mcp_url}/rpc",
            json=navigate_payload,
            headers=headers,
            timeout=30
        )

        if response.status_code == 200:
            result = response.json()
            print("导航成功!")
            print("响应:", json.dumps(result, indent=2, ensure_ascii=False))
        else:
            print(f"请求失败，状态码: {response.status_code}")
            print("响应内容:", response.text)

        # 3. 等待页面加载
        time.sleep(3)

        # 4. 可选：截图
        screenshot_payload = {
            "jsonrpc": "2.0",
            "id": 2,
            "method": "tools/call",
            "params": {
                "name": "playwright_screenshot",
                "arguments": {
                    "name": "google_homepage.png"
                }
            }
        }

        print("尝试截图...")
        screenshot_response = requests.post(
            f"{mcp_url}/rpc",
            json=screenshot_payload,
            headers=headers,
            timeout=30
        )

        if screenshot_response.status_code == 200:
            screenshot_result = screenshot_response.json()
            print("截图成功!")
            print("截图结果:", json.dumps(screenshot_result, indent=2, ensure_ascii=False))
        else:
            print(f"截图失败，状态码: {screenshot_response.status_code}")

    except requests.exceptions.ConnectionError:
        print("无法连接到 MCP 服务器。请确保 playwright-mcp 服务器正在运行。")
        print("启动命令示例: npx @modelcontextprotocol/server-playwright")
    except requests.exceptions.Timeout:
        print("请求超时。服务器可能正在处理请求。")
    except Exception as e:
        print(f"调用 playwright-mcp 时出错: {e}")

def call_playwright_mcp_sse():
    """通过 SSE 调用 playwright-mcp 服务器"""

    sse_url = "http://localhost:8931/sse"

    try:
        # 发送 SSE 请求
        navigate_data = {
            "action": "navigate",
            "url": "https://www.google.com"
        }

        print("通过 SSE 发送导航请求...")
        response = requests.post(sse_url, json=navigate_data, timeout=30)

        if response.status_code == 200:
            print("SSE 导航请求发送成功!")
            print("响应:", response.text)
        else:
            print(f"SSE 请求失败，状态码: {response.status_code}")

    except Exception as e:
        print(f"SSE 调用失败: {e}")

if __name__ == "__main__":
    print("=== 调用 Playwright-MCP 打开谷歌 ===")
    print()

    print("方法1: HTTP JSON-RPC 调用")
    call_playwright_mcp_http()

    print("\n" + "="*50 + "\n")

    print("方法2: SSE 调用")
    call_playwright_mcp_sse()