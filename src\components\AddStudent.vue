<template>
    <!-- @submit.prevent="submitForm" -->
    <el-form :model="form" >
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="姓名">
            <el-input v-model="form.name" placeholder="请输入姓名"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="年龄">
            <el-input v-model.number="form.age" placeholder="请输入年龄"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="成绩">
            <el-select v-model="form.grade" placeholder="请选择成绩">
              <el-option label="A" value="A"></el-option>
              <el-option label="B" value="B"></el-option>
              <el-option label="C" value="C"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item>
            <el-button type="primary" @click="submitForm">添加学生</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </template>
  
  <script>
  export default {
    name: 'AddStudent',
    data() {
      return {
        form: {
          name: '',
          age: '',
          grade: ''
        }
      }
    },
    methods: {
      submitForm() {
        console.log('submitForm 被调用');

        if (this.form.name && this.form.age && this.form.grade) {
          this.$emit('add-student', { ...this.form })
          this.form.name = ''
          this.form.age = ''
          this.form.grade = ''
        } else {
          this.$message.error('请填写所有字段')
        }
      }
    }
  }
  </script>