#!/usr/bin/env node
/**
 * 调用本地 playwright-mcp 服务器打开谷歌
 * 使用 Node.js 和 HTTP 请求
 */

const http = require('http');
const https = require('https');

// MCP 服务器配置
const MCP_HOST = 'localhost';
const MCP_PORT = 8931;

/**
 * 发送 JSON-RPC 请求到 MCP 服务器
 */
function sendMCPRequest(method, params, id = 1) {
    return new Promise((resolve, reject) => {
        const payload = JSON.stringify({
            jsonrpc: "2.0",
            id: id,
            method: method,
            params: params
        });

        const options = {
            hostname: MCP_HOST,
            port: MCP_PORT,
            path: '/rpc',
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(payload)
            }
        };

        const req = http.request(options, (res) => {
            let data = '';

            res.on('data', (chunk) => {
                data += chunk;
            });

            res.on('end', () => {
                try {
                    const response = JSON.parse(data);
                    resolve(response);
                } catch (error) {
                    reject(new Error(`解析响应失败: ${error.message}`));
                }
            });
        });

        req.on('error', (error) => {
            reject(new Error(`请求失败: ${error.message}`));
        });

        req.write(payload);
        req.end();
    });
}

/**
 * 检查 MCP 服务器状态
 */
async function checkMCPServer() {
    try {
        console.log('检查 MCP 服务器状态...');
        const response = await sendMCPRequest('tools/list', {});
        console.log('✅ MCP 服务器运行正常');
        
        if (response.result && response.result.tools) {
            console.log('可用工具:', response.result.tools.map(tool => tool.name));
        }
        
        return true;
    } catch (error) {
        console.log('❌ MCP 服务器连接失败:', error.message);
        console.log('请确保 playwright-mcp 服务器正在运行');
        console.log('启动命令: npx @modelcontextprotocol/server-playwright');
        return false;
    }
}

/**
 * 使用 Playwright 导航到谷歌
 */
async function navigateToGoogle() {
    try {
        console.log('\n🌐 正在导航到谷歌...');
        
        const response = await sendMCPRequest('tools/call', {
            name: 'playwright_navigate',
            arguments: {
                url: 'https://www.google.com',
                wait_for: 'networkidle'
            }
        });

        if (response.result) {
            console.log('✅ 成功打开谷歌!');
            console.log('响应:', JSON.stringify(response.result, null, 2));
        } else if (response.error) {
            console.log('❌ 导航失败:', response.error.message);
        }

        return response;
    } catch (error) {
        console.log('❌ 导航请求失败:', error.message);
        throw error;
    }
}

/**
 * 截图保存
 */
async function takeScreenshot(filename = 'google_homepage.png') {
    try {
        console.log('\n📸 正在截图...');
        
        const response = await sendMCPRequest('tools/call', {
            name: 'playwright_screenshot',
            arguments: {
                name: filename
            }
        });

        if (response.result) {
            console.log('✅ 截图成功!');
            console.log('文件名:', filename);
            console.log('响应:', JSON.stringify(response.result, null, 2));
        } else if (response.error) {
            console.log('❌ 截图失败:', response.error.message);
        }

        return response;
    } catch (error) {
        console.log('❌ 截图请求失败:', error.message);
        throw error;
    }
}

/**
 * 获取页面内容
 */
async function getPageContent() {
    try {
        console.log('\n📄 获取页面内容...');
        
        const response = await sendMCPRequest('tools/call', {
            name: 'playwright_get_page_content',
            arguments: {}
        });

        if (response.result) {
            console.log('✅ 获取页面内容成功!');
            // 只显示前500个字符，避免输出过长
            const content = response.result.content || '';
            console.log('页面内容预览:', content.substring(0, 500) + (content.length > 500 ? '...' : ''));
        } else if (response.error) {
            console.log('❌ 获取页面内容失败:', response.error.message);
        }

        return response;
    } catch (error) {
        console.log('❌ 获取页面内容请求失败:', error.message);
        throw error;
    }
}

/**
 * 主函数
 */
async function main() {
    console.log('=== Playwright-MCP 客户端 ===');
    console.log('目标: 打开谷歌网站\n');

    try {
        // 1. 检查服务器状态
        const serverOk = await checkMCPServer();
        if (!serverOk) {
            process.exit(1);
        }

        // 2. 导航到谷歌
        await navigateToGoogle();

        // 等待页面加载
        console.log('\n⏳ 等待页面加载...');
        await new Promise(resolve => setTimeout(resolve, 3000));

        // 3. 截图
        await takeScreenshot('google_homepage.png');

        // 4. 获取页面内容
        await getPageContent();

        console.log('\n🎉 所有操作完成!');

    } catch (error) {
        console.error('❌ 执行过程中出现错误:', error.message);
        process.exit(1);
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    main().catch(console.error);
}

module.exports = {
    sendMCPRequest,
    checkMCPServer,
    navigateToGoogle,
    takeScreenshot,
    getPageContent
};
