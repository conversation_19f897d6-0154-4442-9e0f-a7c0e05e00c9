const { test, expect } = require('@playwright/test');

test.describe('Playwright.dev Website Tests', () => {
  test('should visit playwright.dev and interact with Get started button', async ({ page }) => {
    // 访问 playwright.dev 网站
    await page.goto('https://playwright.dev');
    
    // 验证页面标题包含 "Playwright"
    await expect(page).toHaveTitle(/Playwright/);
    
    // 查找 "Get started" 按钮并验证它是可见的
    const getStartedButton = page.getByRole('link', { name: /get started/i });
    await expect(getStartedButton).toBeVisible();
    
    // 验证按钮文本
    await expect(getStartedButton).toHaveText(/get started/i);
    
    // 点击 "Get started" 按钮
    await getStartedButton.click();
    
    // 验证页面导航到了正确的页面（通常是文档页面）
    await expect(page).toHaveURL(/.*\/docs.*/);
    
    // 验证新页面加载成功，检查是否有文档相关的内容
    await expect(page.locator('h1')).toBeVisible();
  });

  test('should verify Get started button properties', async ({ page }) => {
    // 访问 playwright.dev 网站
    await page.goto('https://playwright.dev');
    
    // 等待页面完全加载
    await page.waitForLoadState('networkidle');
    
    // 查找 "Get started" 按钮
    const getStartedButton = page.getByRole('link', { name: /get started/i });
    
    // 验证按钮是否可见
    await expect(getStartedButton).toBeVisible();
    
    // 验证按钮是否可点击
    await expect(getStartedButton).toBeEnabled();
    
    // 验证按钮的 href 属性（如果是链接）
    const href = await getStartedButton.getAttribute('href');
    expect(href).toBeTruthy();
    
    // 验证按钮在视口中可见
    await expect(getStartedButton).toBeInViewport();
    
    // 可选：验证按钮的样式属性
    await expect(getStartedButton).toHaveCSS('cursor', 'pointer');
  });

  test('should handle Get started button click with navigation', async ({ page }) => {
    // 访问 playwright.dev 网站
    await page.goto('https://playwright.dev');
    
    // 查找 "Get started" 按钮
    const getStartedButton = page.getByRole('link', { name: /get started/i });
    
    // 验证按钮存在且可见
    await expect(getStartedButton).toBeVisible();
    
    // 使用 Promise.all 来等待导航和点击同时完成
    const [response] = await Promise.all([
      page.waitForNavigation({ waitUntil: 'networkidle' }),
      getStartedButton.click()
    ]);
    
    // 验证响应状态码
    expect(response.status()).toBe(200);
    
    // 验证新页面的 URL
    expect(page.url()).toMatch(/docs/);
    
    // 验证新页面有预期的内容
    await expect(page.locator('nav')).toBeVisible();
    await expect(page.locator('main')).toBeVisible();
  });

  test('should verify page accessibility and Get started button', async ({ page }) => {
    // 访问 playwright.dev 网站
    await page.goto('https://playwright.dev');
    
    // 查找 "Get started" 按钮
    const getStartedButton = page.getByRole('link', { name: /get started/i });
    
    // 验证按钮的可访问性属性
    await expect(getStartedButton).toBeVisible();
    
    // 验证按钮有适当的角色
    const role = await getStartedButton.getAttribute('role');
    expect(role === 'link' || role === 'button' || role === null).toBeTruthy();
    
    // 验证按钮可以通过键盘访问
    await getStartedButton.focus();
    await expect(getStartedButton).toBeFocused();
    
    // 使用 Enter 键点击按钮
    await page.keyboard.press('Enter');
    
    // 验证导航成功
    await expect(page).toHaveURL(/.*\/docs.*/);
  });

  test('should verify responsive behavior of Get started button', async ({ page }) => {
    // 测试不同视口大小下的按钮行为
    
    // 桌面视口
    await page.setViewportSize({ width: 1200, height: 800 });
    await page.goto('https://playwright.dev');
    
    let getStartedButton = page.getByRole('link', { name: /get started/i });
    await expect(getStartedButton).toBeVisible();
    
    // 平板视口
    await page.setViewportSize({ width: 768, height: 1024 });
    await page.reload();
    
    getStartedButton = page.getByRole('link', { name: /get started/i });
    await expect(getStartedButton).toBeVisible();
    
    // 手机视口
    await page.setViewportSize({ width: 375, height: 667 });
    await page.reload();
    
    getStartedButton = page.getByRole('link', { name: /get started/i });
    await expect(getStartedButton).toBeVisible();
    
    // 在手机视口下点击按钮
    await getStartedButton.click();
    await expect(page).toHaveURL(/.*\/docs.*/);
  });
});
