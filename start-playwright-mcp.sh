#!/bin/bash

# 启动 Playwright-MCP 服务器的脚本

echo "=== 启动 Playwright-MCP 服务器 ==="
echo

# 检查是否安装了 Node.js
if ! command -v node &> /dev/null; then
    echo "❌ 错误: 未找到 Node.js"
    echo "请先安装 Node.js: https://nodejs.org/"
    exit 1
fi

# 检查是否安装了 npm
if ! command -v npm &> /dev/null; then
    echo "❌ 错误: 未找到 npm"
    exit 1
fi

echo "✅ Node.js 版本: $(node --version)"
echo "✅ npm 版本: $(npm --version)"
echo

# 检查是否已安装 playwright-mcp
echo "📦 检查 @modelcontextprotocol/server-playwright..."

if ! npm list -g @modelcontextprotocol/server-playwright &> /dev/null; then
    echo "⚠️  未找到 @modelcontextprotocol/server-playwright，正在安装..."
    npm install -g @modelcontextprotocol/server-playwright
    
    if [ $? -ne 0 ]; then
        echo "❌ 安装失败，尝试本地安装..."
        npm install @modelcontextprotocol/server-playwright
    fi
else
    echo "✅ @modelcontextprotocol/server-playwright 已安装"
fi

echo

# 启动服务器
echo "🚀 启动 Playwright-MCP 服务器..."
echo "服务器将运行在: http://localhost:8931"
echo "按 Ctrl+C 停止服务器"
echo

# 尝试不同的启动方式
if command -v npx &> /dev/null; then
    echo "使用 npx 启动..."
    npx @modelcontextprotocol/server-playwright --port 8931
elif [ -f "node_modules/.bin/playwright-mcp" ]; then
    echo "使用本地安装启动..."
    ./node_modules/.bin/playwright-mcp --port 8931
else
    echo "❌ 无法启动服务器"
    echo "请手动安装: npm install -g @modelcontextprotocol/server-playwright"
    exit 1
fi
