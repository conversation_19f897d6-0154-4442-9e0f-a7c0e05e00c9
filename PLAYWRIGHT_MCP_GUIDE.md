# Playwright-MCP 使用指南

本指南将帮助您设置和使用 Playwright-MCP 来自动化浏览器操作，包括打开谷歌网站。

## 📋 前置要求

1. **Node.js** (版本 16 或更高)
   - 下载地址: https://nodejs.org/
   - 验证安装: `node --version`

2. **npm** (通常随 Node.js 一起安装)
   - 验证安装: `npm --version`

3. **Python** (如果使用 Python 客户端)
   - 下载地址: https://python.org/
   - 验证安装: `python --version`

## 🚀 快速开始

### 方法 1: 使用 JavaScript 客户端 (推荐)

1. **启动 Playwright-MCP 服务器**

   **Windows:**
   ```bash
   # 双击运行或在命令行执行
   start-playwright-mcp.bat
   ```

   **Linux/Mac:**
   ```bash
   # 给脚本执行权限
   chmod +x start-playwright-mcp.sh
   # 运行脚本
   ./start-playwright-mcp.sh
   ```

   **手动启动:**
   ```bash
   # 安装 playwright-mcp 服务器
   npm install -g @modelcontextprotocol/server-playwright

   # 启动服务器
   npx @modelcontextprotocol/server-playwright --port 8931
   ```

2. **运行客户端脚本**

   在新的终端窗口中:
   ```bash
   # 运行 JavaScript 客户端
   node playwright-mcp-client.js
   ```

### 方法 2: 使用 Python 客户端

1. **安装 Python 依赖**
   ```bash
   pip install requests
   ```

2. **启动 MCP 服务器** (同上)

3. **运行 Python 客户端**
   ```bash
   python src/te
   ```

## 📁 文件说明

- `playwright-mcp-client.js` - JavaScript 客户端，用于调用 MCP 服务器
- `src/te` - Python 客户端脚本
- `start-playwright-mcp.sh` - Linux/Mac 启动脚本
- `start-playwright-mcp.bat` - Windows 启动脚本
- `playwright-test.spec.js` - Playwright 测试示例

## 🔧 配置说明

### MCP 服务器配置

默认配置:
- **主机**: localhost
- **端口**: 8931
- **协议**: HTTP JSON-RPC

如需修改，请编辑客户端脚本中的配置:

```javascript
// JavaScript 客户端
const MCP_HOST = 'localhost';
const MCP_PORT = 8931;
```

```python
# Python 客户端
mcp_url = "http://localhost:8931"
```

## 🎯 支持的操作

### 1. 导航到网页
```javascript
await sendMCPRequest('tools/call', {
    name: 'playwright_navigate',
    arguments: {
        url: 'https://www.google.com',
        wait_for: 'networkidle'
    }
});
```

### 2. 截图
```javascript
await sendMCPRequest('tools/call', {
    name: 'playwright_screenshot',
    arguments: {
        name: 'screenshot.png'
    }
});
```

### 3. 获取页面内容
```javascript
await sendMCPRequest('tools/call', {
    name: 'playwright_get_page_content',
    arguments: {}
});
```

### 4. 点击元素
```javascript
await sendMCPRequest('tools/call', {
    name: 'playwright_click',
    arguments: {
        selector: 'button[type="submit"]'
    }
});
```

### 5. 输入文本
```javascript
await sendMCPRequest('tools/call', {
    name: 'playwright_type',
    arguments: {
        selector: 'input[name="q"]',
        text: '搜索内容'
    }
});
```

## 🐛 故障排除

### 问题 1: 无法连接到 MCP 服务器

**解决方案:**
1. 确保 MCP 服务器正在运行
2. 检查端口 8931 是否被占用
3. 验证防火墙设置

### 问题 2: 导入模块失败

**Python 客户端:**
```bash
pip install requests mcp
```

**JavaScript 客户端:**
```bash
npm install @modelcontextprotocol/server-playwright
```

### 问题 3: Playwright 浏览器未安装

```bash
# 安装 Playwright 浏览器
npx playwright install
```

## 📝 示例用法

### 完整的谷歌搜索示例

```javascript
async function searchGoogle(query) {
    // 1. 导航到谷歌
    await navigateToGoogle();
    
    // 2. 等待页面加载
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 3. 输入搜索内容
    await sendMCPRequest('tools/call', {
        name: 'playwright_type',
        arguments: {
            selector: 'input[name="q"]',
            text: query
        }
    });
    
    // 4. 点击搜索按钮
    await sendMCPRequest('tools/call', {
        name: 'playwright_click',
        arguments: {
            selector: 'input[type="submit"]'
        }
    });
    
    // 5. 截图保存结果
    await takeScreenshot(`search_${query}.png`);
}

// 使用示例
searchGoogle('Playwright MCP');
```

## 🔗 相关链接

- [Playwright 官方文档](https://playwright.dev/)
- [Model Context Protocol](https://modelcontextprotocol.io/)
- [Node.js 官网](https://nodejs.org/)
- [Python 官网](https://python.org/)

## 📞 支持

如果遇到问题，请检查:
1. 所有依赖是否正确安装
2. 服务器是否正在运行
3. 网络连接是否正常
4. 端口是否被占用
