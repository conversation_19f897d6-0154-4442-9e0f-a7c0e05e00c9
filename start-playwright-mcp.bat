@echo off
chcp 65001 >nul

echo === 启动 Playwright-MCP 服务器 ===
echo.

REM 检查是否安装了 Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到 Node.js
    echo 请先安装 Node.js: https://nodejs.org/
    pause
    exit /b 1
)

REM 检查是否安装了 npm
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到 npm
    pause
    exit /b 1
)

echo ✅ Node.js 版本:
node --version
echo ✅ npm 版本:
npm --version
echo.

REM 检查是否已安装 playwright-mcp
echo 📦 检查 @modelcontextprotocol/server-playwright...

npm list -g @modelcontextprotocol/server-playwright >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  未找到 @modelcontextprotocol/server-playwright，正在安装...
    npm install -g @modelcontextprotocol/server-playwright
    
    if %errorlevel% neq 0 (
        echo ❌ 全局安装失败，尝试本地安装...
        npm install @modelcontextprotocol/server-playwright
    )
) else (
    echo ✅ @modelcontextprotocol/server-playwright 已安装
)

echo.

REM 启动服务器
echo 🚀 启动 Playwright-MCP 服务器...
echo 服务器将运行在: http://localhost:8931
echo 按 Ctrl+C 停止服务器
echo.

REM 尝试不同的启动方式
where npx >nul 2>&1
if %errorlevel% equ 0 (
    echo 使用 npx 启动...
    npx @modelcontextprotocol/server-playwright --port 8931
) else if exist "node_modules\.bin\playwright-mcp.cmd" (
    echo 使用本地安装启动...
    node_modules\.bin\playwright-mcp.cmd --port 8931
) else (
    echo ❌ 无法启动服务器
    echo 请手动安装: npm install -g @modelcontextprotocol/server-playwright
    pause
    exit /b 1
)

pause
