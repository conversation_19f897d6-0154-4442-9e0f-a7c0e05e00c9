#!/usr/bin/env node
/**
 * 测试 MCP 连接的简单脚本
 */

const http = require('http');

const MCP_HOST = 'localhost';
const MCP_PORT = 8931;

function testConnection() {
    return new Promise((resolve, reject) => {
        const options = {
            hostname: MCP_HOST,
            port: MCP_PORT,
            path: '/health',
            method: 'GET',
            timeout: 5000
        };

        const req = http.request(options, (res) => {
            console.log(`✅ MCP 服务器响应: ${res.statusCode}`);
            resolve(true);
        });

        req.on('error', (error) => {
            console.log(`❌ 连接失败: ${error.message}`);
            console.log('请确保 MCP 服务器正在运行:');
            console.log('  npm run start-mcp-server');
            console.log('  或者');
            console.log('  npx @modelcontextprotocol/server-playwright --port 8931');
            reject(false);
        });

        req.on('timeout', () => {
            console.log('❌ 连接超时');
            req.destroy();
            reject(false);
        });

        req.end();
    });
}

async function main() {
    console.log('🔍 测试 MCP 服务器连接...');
    console.log(`目标: http://${MCP_HOST}:${MCP_PORT}`);
    console.log();

    try {
        await testConnection();
        console.log();
        console.log('🎉 连接成功! 您可以运行:');
        console.log('  npm run playwright-mcp');
        console.log('  或者');
        console.log('  node playwright-mcp-client.js');
    } catch (error) {
        console.log();
        console.log('💡 提示: 如果您还没有启动 MCP 服务器，请先运行:');
        console.log('  npm run start-mcp-server');
        process.exit(1);
    }
}

if (require.main === module) {
    main().catch(console.error);
}
